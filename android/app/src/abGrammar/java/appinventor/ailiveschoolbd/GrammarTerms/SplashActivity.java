package appinventor.ailiveschoolbd.GrammarTerms;

import android.app.Activity;
import android.os.Bundle;
import android.util.Base64;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ProgressBar;

import java.io.FileInputStream;
import java.io.FileOutputStream;

import javax.crypto.Cipher;
import javax.crypto.CipherOutputStream;
import javax.crypto.spec.SecretKeySpec;

public class SplashActivity extends Activity
{
    static {
        System.loadLibrary("mhjtmi");
    }

    public native void checkNullStr(Activity activity);

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        FrameLayout layout = new FrameLayout(this);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT,
                Gravity.CENTER
        );
        ProgressBar progressBar = new ProgressBar(this);
        progressBar.setIndeterminate(true);
        layout.addView(progressBar, params);
        setContentView(layout);

        checkNullStr(this);
    }
}