import 'dart:io';

import 'package:and/app.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/http/interceptor.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/l10n/language_utils.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/deeplink_utils.dart';
import 'package:and/utils/device_utils.dart';
import 'package:and/utils/sign_utils.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:oktoast/oktoast.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:android_play_install_referrer/android_play_install_referrer.dart';

import 'http_config.dart';

var loginExpired = [602, 101008, 101006, 608];

class MyHttp {
  static final BaseOptions options = BaseOptions(
      baseUrl: HttpConfig.baseApiURL,
      connectTimeout: const Duration(seconds: 60),
      sendTimeout: const Duration(seconds: 60),
      receiveTimeout: const Duration(seconds: 60));
  static final Dio dio = Dio(options)
    ..interceptors.add(ErrorMessageInterceptor())
    ..interceptors.add(LogInterceptor())
    ..interceptors.add(InterceptorsWrapper(onRequest:
        (RequestOptions options, RequestInterceptorHandler handler) async {
      if (CacheHelper.devTestMode) {
        options.baseUrl = options.baseUrl
            .replaceAll(HttpConfig.baseApiURL, HttpConfig.baseTestApiURL);
      }

      if (kDebugMode) {
        print("原始的网络请求 url: ${options.uri}");
      }

      var headers = await addHeaders();
      options.headers.addAll(headers);

      handler.next(options);
    }, onResponse: (
      response,
      ResponseInterceptorHandler handler,
    ) async {
      if (kDebugMode) {
        print("原始的网络请求响应response: $response");
      }

      handler.next(response);
    }, onError: (
      DioException error,
      ErrorInterceptorHandler handler,
    ) {
      print("拦截了错误error: $error");
      if (globalContext != null) {
        if (error.response?.statusCode == 401 && CacheHelper.token != null) {
          if (globalContext != null) {
            showToast(globalContext!.l10n.loginExpired);
          }
          CommonHelper.exitLogin(isDeleteToken: false);
        } else if (error.response?.data["msg_code"] == 209) {
          showToast(globalContext!.l10n.smsSendTooFrequently);
        } else if (error.response?.data["msg_code"] == 101) {
          showToast(globalContext!.l10n.invitorNotExist);
        } else if (error.response?.data["msg_code"] == 200) {
          showToast(globalContext!.l10n.accountNotExist);
        } else if (error.response?.data["msg_code"] == 201) {
          showToast(globalContext!.l10n.accountPasswordError);
        } else if (error.response?.data["msg_code"] == 205) {
          showToast(globalContext!.l10n.phoneNumberExists);
        } else if (error.response?.data["msg_code"] == 207) {
          showToast(globalContext!.l10n.emailExists);
        } else if (error.response?.data["msg_code"] == 210) {
          showToast(globalContext!.l10n.verifyCodeError);
        } else if (error.response?.data["msg_code"] == 211) {
          showToast(globalContext!.l10n.shortNoRule);
        } else if (error.response?.data["msg_code"] == 212) {
          showToast(globalContext!.l10n.onlyModifyOnce);
        } else if (error.response?.data["msg_code"] == 400) {
          showToast(globalContext!.l10n.phoneNumberNotInTargetRegion);
        } else if (error.response?.data["msg_code"] == 602) {
          showToast(globalContext!.l10n.msgCannotPin);
        } else if (error.response?.data["msg_code"] == 603) {
          showToast(globalContext!.l10n.msgCannotUnpin);
        }
      }

      handler.next(error);
    }));

  static Future<Map<String, String>> addHeaders() async {
    var headers = <String, String>{};
    var token = CacheHelper.token ?? DeeplinkUtils.visitorToken ?? "";
    headers["appid"] = "wukongchat";
    if (token.isNotEmpty) {
      headers["token"] = token;
    }
    headers["model"] = await DeviceUtils.getDeviceModel();
    if (Platform.isIOS) {
      headers["os"] = "iOS";
    } else {
      headers["os"] = "Android";
    }
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    var appVersion = await DeviceUtils.getAppVersion();
    headers["version"] = appVersion;
    headers["package"] = packageInfo.packageName;
    headers["timestamp"] = timestamp.toString();
    headers["sign"] = SignUtils.signPackage(
        timestamp: timestamp,
        version: appVersion,
        packageName: packageInfo.packageName);
    headers["language"] = LanguageUtils.language();
    headers["Device-Id"] = await DeviceUtils.getDeviceId();
    if (Platform.isAndroid) {
      try {
        ReferrerDetails referrerDetails =
            await AndroidPlayInstallReferrer.installReferrer;

        headers["Referrer-Url"] = referrerDetails.installReferrer ?? '';
      } catch (e) {
        if (kDebugMode) {
          print("获取安装来源失败: $e");
        }
      }
    }

    return headers;
  }
}
