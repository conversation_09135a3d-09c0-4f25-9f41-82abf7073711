import 'package:and/utils/image_path.dart';
import 'package:and/vest/period_tracker_app.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';

class GuidePage extends StatefulWidget {
  final List<String> images;
  final WidgetBuilder builder;
  const GuidePage({super.key, required this.images, required this.builder});

  @override
  State<GuidePage> createState() => _GuidePageState();
}

class _GuidePageState extends State<GuidePage> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  late List<Widget> _pages = widget.images.map((image) => _buildPage(image)).toList();

  static Widget _buildPage(String image) {
    return Image.asset(
      image,
      fit: BoxFit.fitHeight
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
          child: Stack(
        children: [
          PageView(
            controller: _pageController,
            onPageChanged: (int page) {
              setState(() {
                _currentPage = page;
              });
            },
            children: _pages,
          ),
          Positioned(
            bottom: 20,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(_pages.length, (index) {
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _currentPage == index
                        ? Colors.white
                        : Colors.white.withOpacity(0.5),
                  ),
                );
              }),
            ),
          ),
          Positioned(
            bottom: 40,
            left: 0,
            right: 0,
            child: Visibility(
                visible: _currentPage == _pages.length - 1,
                child: SizedBox(
                  width: 300,
                  child: Center(
                    child: SubmitButton(
                        mainAxisSize: MainAxisSize.min,
                        onPressed: () {
                          Navigator.pushReplacement(
                              context,
                              MaterialPageRoute(
                                  builder: widget.builder));
                        },
                        text: "Get Started"),
                  ),
                )),
          ),
        ],
      )),
    );
  }
}
