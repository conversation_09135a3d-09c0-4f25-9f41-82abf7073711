import 'package:and/utils/image_path.dart';
import 'package:and/vest/guide_page.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart'; // Ensure intl is added in pubspec.yaml

class PeriodTrackerApp extends StatelessWidget {
  const PeriodTrackerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Period Tracker',
      theme: ThemeData(primarySwatch: Colors.pink),
      debugShowCheckedModeBanner: false,
      home: GuidePage(
        images: [
          ImagePath.bg_abicon_startup,
          ImagePath.bg_abicon_welcome,
          ImagePath.bg_abicon_gleezy,
        ],
        builder: (context) => PeriodTrackerPage(),
      ),
    );
  }
}

class PeriodTrackerPage extends StatefulWidget {
  const PeriodTrackerPage({super.key});

  @override
  State<PeriodTrackerPage> createState() => _PeriodTrackerPageState();
}

class _PeriodTrackerPageState extends State<PeriodTrackerPage> {
  DateTime? lastPeriod;
  int cycleLength = 28;
  DateTime? nextPeriod;

  void calculateNextPeriod() {
    if (lastPeriod != null) {
      setState(() {
        nextPeriod = lastPeriod!.add(Duration(days: cycleLength));
      });
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: lastPeriod ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != lastPeriod) {
      setState(() {
        lastPeriod = picked;
        nextPeriod = null;
      });
      calculateNextPeriod();
    }
  }

  @override
  Widget build(BuildContext context) {
    String lastPeriodText = lastPeriod != null
        ? DateFormat.yMMMd().format(lastPeriod!)
        : 'Select Date';
    String nextPeriodText =
        nextPeriod != null ? DateFormat.yMMMd().format(nextPeriod!) : '-';
    return Scaffold(
      appBar: AppBar(title: Text('Period Tracker')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            ListTile(
              title: Text('Last Period Start Date'),
              trailing: Text(lastPeriodText, style: TextStyle(fontSize: 16)),
              onTap: () => _selectDate(context),
            ),
            SizedBox(height: 20),
            Row(
              children: [
                Text('Cycle Length (days): ', style: TextStyle(fontSize: 16)),
                Expanded(
                  child: Slider(
                    value: cycleLength.toDouble(),
                    min: 21,
                    max: 35,
                    divisions: 14,
                    label: cycleLength.toString(),
                    onChanged: (value) {
                      setState(() {
                        cycleLength = value.toInt();
                        calculateNextPeriod();
                      });
                    },
                  ),
                ),
                Text(cycleLength.toString(), style: TextStyle(fontSize: 16)),
              ],
            ),
            SizedBox(height: 20),
            Card(
              elevation: 4,
              child: ListTile(
                title: Text('Next Expected Period'),
                trailing: Text(nextPeriodText,
                    style:
                        TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
