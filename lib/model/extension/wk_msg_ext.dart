import 'dart:convert';

import 'package:and/app.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/constant/wk_channel_member_role.dart';
import 'package:and/constant/wk_message_content_type_ext.dart';
import 'package:and/http/my_http.dart';
import 'package:and/io/user.dart' show UserApi;
import 'package:and/l10n/l10n.dart';
import 'package:and/model/content/wk_call_content.dart';
import 'package:and/model/content/wk_multi_forward_content.dart';
import 'package:and/model/extension/user_info_ext.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/model/extension/wx_channel_member_ext.dart';
import 'package:and/module/videocall/videocall_argument.dart';
import 'package:and/utils/time_utils.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/channel_member.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

extension WKMsgListExtension on List<WKMsg> {
  Future<WKMultiForwardContent> toMergeMsg() async {
    WKMultiForwardContent forwardContent = WKMultiForwardContent();
    forwardContent.channelType =
        firstOrNull?.channelType ?? WKChannelType.personal;
    forwardContent.channelId = firstOrNull?.channelID ?? '';
    forwardContent.uid = CacheHelper.uid ?? '';
    var userList = <WKChannel>[];
    for (var msg in this) {
      var isAdded = userList.any((e) => e.channelID == msg.fromUID);
      if (isAdded) {
        continue;
      }
      var from = await msg.getFromAsync();
      if (from != null) {
        userList.add(from);
      }
    }
    forwardContent.msgList = this;
    forwardContent.userList = userList;
    return forwardContent;
  }
}

extension WkMsgExtension on WKMsg {
  bool get isSystemMsg {
    return WkMessageContentTypeExt.isSystemMsg(contentType);
  }

  bool get isNormalMsg {
    var isRevoke = wkMsgExtra?.revoke == 1;
    var isSuccess = status == WKSendMsgResult.sendSuccess;
    if (isSystemMsg || isRevoke || !isSuccess) {
      return false;
    }
    return true;
  }

  WKReply toReply() {
    WKReply wkReply = WKReply();
    wkReply.payload = wkMsgExtra?.messageContent ?? messageContent;
    // wkReply.fromName = await fromName;
    wkReply.fromUID = fromUID;
    wkReply.messageId = messageID;
    wkReply.messageSeq = messageSeq;
    wkReply.rootMid = messageContent?.reply?.rootMid ?? messageID;
    return wkReply;
  }

  String get formatTime {
    var time = timestamp;
    if ((wkMsgExtra?.editedAt ?? 0) != 0) {
      time = wkMsgExtra?.editedAt ?? 0;
    }
    return TimeUtils.formatHHMM(time);
  }

  Future<WKChannelMember?> getMemberOfFromAsync() async {
    var memberFrom = getMemberOfFrom();
    if (memberFrom == null) {
      var member = await WKIM.shared.channelMemberManager
          .getMember(channelID, channelType, fromUID);
      if (member != null) {
        memberFrom = member;
        setMemberOfFrom(member);
      }
    }
    return memberFrom;
  }

  Future<WKChannel?> getFromAsync() async {
    var channelFrom = getFrom();
    if (channelFrom == null) {
      var from = await WKIM.shared.channelManager
          .getChannel(fromUID, WKChannelType.personal);
      if (from != null) {
        channelFrom = from;
        setFrom(from);
      }
    }
    return channelFrom;
  }

  Future<String> get fromName async {
    String fromName = '';
    if ((WkMessageContentTypeExt.isSystemMsg(contentType) ||
        contentType == WkMessageContentTypeExt.revoke ||
        wkMsgExtra?.revoke == 1 ||
        contentType == WkMessageContentTypeExt.screenshot)) {
      return fromName;
    }
    if (channelType == WKChannelType.personal ||
        channelType == WKChannelType.customerService ||
        fromUID.isEmpty ||
        fromUID == CacheHelper.uid) {
      return fromName;
    }
    fromName = await fromNameDisplay;
    return fromName;
  }

  Future<String> get fromNameDisplay async {
    String memberDisplay = '';
    String channelDisplay = '';
    if (fromUID == CacheHelper.uid) {
      return globalContext?.l10n.you ?? '';
    }
    if (channelType == WKChannelType.group) {
      var memberFrom = await getMemberOfFromAsync();
      memberDisplay = memberFrom?.displayName ?? '';
      if (memberDisplay.isNotEmpty) {
        return memberDisplay;
      }
      var channelInfo = getChannelInfo();
      var isAnonymous = channelInfo?.anonymous == 1;
      //如果是匿名群
      if (isAnonymous) {
        channelDisplay = globalContext?.l10n.anonymous ?? '';
      } else {
        var channelFrom = await getFromAsync();
        channelDisplay = channelFrom?.displayName ?? '';
      }
    } else {
      var channelFrom = await getFromAsync();
      channelDisplay = channelFrom?.displayName ?? '';
    }
    return channelDisplay;
  }

  /// 系统消息
  String get showSystemContent {
    var context = globalContext;
    if (context == null) return '';
    String contentJson = content ?? '';
    String showContent;
    String loginUID = CacheHelper.userProfile?.uid ?? '';
    try {
      if (contentJson.isEmpty) {
        return "";
      }
      Map<String, dynamic> jsonObject = json.decode(contentJson);
      String string = jsonObject['content'] ?? '';
      List<dynamic>? list = jsonObject['extra'];
      List<String> names = [];
      if (list != null && list.isNotEmpty) {
        for (var item in list) {
          String name = '';
          if (item is Map<String, dynamic>) {
            name = item['name'] ?? '';
            if (item.containsKey('uid')) {
              String uid = item['uid'] ?? '';
              if (uid.isNotEmpty && uid == loginUID) {
                name = context.l10n.you;
              }
            }
          }
          names.add(name);
        }
      }
      if (names.isNotEmpty) {
        showContent = string.replaceAllMapped(RegExp(r'\{(\d+)\}'), (match) {
          int index = int.parse(match.group(1)!);
          return index < names.length ? names[index] : '';
        });
      } else {
        showContent = string;
      }
    } catch (e) {
      showContent = context.l10n.chatMessageUnknown;
    }
    if (showContent.isEmpty) {
      showContent = context.l10n.chatMessageUnknown;
    }
    return showContent;
  }

  /// 撤回消息
  Future<String> get showRevokeMsg async {
    var context = globalContext;
    if (context == null) return "";
    var content = "";
    if (wkMsgExtra?.revoker != null && fromUID.isNotEmpty) {
      if (wkMsgExtra?.revoker == fromUID) {
        if (fromUID == CacheHelper.uid) {
          content = context.l10n.myRevokeMsg;
        } else {
          var showName = await fromNameDisplay;
          if (showName.isEmpty) {
            WKIM.shared.channelManager
                .fetchChannelInfo(fromUID, WKChannelType.personal);
          }
          content = context.l10n.userRevokeMsg(showName);
        }
      } else {
        var showName = "";
        if (wkMsgExtra?.revoker == CacheHelper.uid) {
          // 你撤回了一条成员''的消息
          showName = await fromNameDisplay;
          content = context.l10n.managerRevokeUserMsg(showName);
        } else {
          // ''撤回了一条成员消息
          var member = await WKIM.shared.channelMemberManager
              .getMember(channelID, channelType, wkMsgExtra!.revoker);
          if (member != null) {
            showName = member.displayName;
          }
          if (showName.isEmpty) {
            var channel = await WKIM.shared.channelManager
                .getChannel(wkMsgExtra!.revoker, WKChannelType.personal);
            if (channel != null) {
              showName = channel.displayName;
            }
          }
          content = context.l10n.managerRevokeUserMsg1(showName);
        }
      }
    } else {
      if (fromUID != CacheHelper.uid) {
        var showName = await fromNameDisplay;
        content = context.l10n.userRevokeMsg(showName);
      } else {
        content = context.l10n.myRevokeMsg;
      }
    }
    return content;
  }

  Future<String> get convContent async {
    var channel = getChannelInfo();
    var isAnonymous = channel?.anonymous == 1;
    if (isAnonymous) {
      var member = await WKIM.shared.channelMemberManager
          .getMember(channelID, channelType, CacheHelper.uid ?? '');
      var role = member?.role ?? WKChannelMemberRole.normal;
      isAnonymous = role == WKChannelMemberRole.normal;
    }
    var isSetChatPwd = channel?.chatPwdOn == 1;
    if (isSetChatPwd) {
      return "❊❊❊❊❊❊❊❊❊❊❊❊❊";
    }
    var content = await previewContent;
    var fromName = await this.fromName;
    if (fromName.isNotEmpty && content.isNotEmpty) {
      content = "$fromName: $content";
    }
    return content;
  }

  /// conversation 预览文本
  Future<String> get previewContent async {
    var context = globalContext;
    if (context == null) return "";
    String content = '';
    try {
      if (isDeleted == 1) return "";
      if (messageContent != null) {
        content = messageContent!.displayText();
      }

      if (content.isEmpty || WkMessageContentTypeExt.isSystemMsg(contentType)) {
        content = showSystemContent;
      }
      if (wkMsgExtra?.messageContent != null) {
        content = wkMsgExtra?.messageContent?.displayText() ?? '';
      }
      //判断是否被撤回
      if (wkMsgExtra?.revoke == 1) {
        content = await showRevokeMsg;
      } else if (contentType == WkMessageContentTypeExt.contentFormatError) {
        content = context.l10n.contentFormatErr;
      } else if (contentType == WkMessageContentTypeExt.signalDecryptError) {
        content = context.l10n.signalDecryptErr;
      } else if (contentType == WkMessageContentTypeExt.noRelation) {
        content =
            context.l10n.noRelationRequest(getChannelInfo()?.displayName ?? "");
      } else if (WkMessageContentTypeExt.isCallMsg(contentType)) {
        WkCallContent? callContent = WkCallContent()
            .decodeJson(jsonDecode(this.content)) as WkCallContent?;
        content = callContent?.callType == CallType.audio.value
            ? context.l10n.audioCall
            : context.l10n.videoCall;
        content = "[$content]";
      }
    } catch (e) {
      print('Error fetching content: $e');
    }
    return content;
  }

  Future<bool> canRevoke() async {
    if (!isNormalMsg) {
      return false;
    }
    bool isManager = false;
    if (channelType == WKChannelType.group) {
      WKChannelMember? member = await WKIM.shared.channelMemberManager
          .getMember(channelID, channelType, CacheHelper.uid ?? '');
      if (member != null && member.role != WKChannelMemberRole.normal) {
        isManager = true;
      }
    }
    if ((fromUID == CacheHelper.uid || isManager)) {
      return true;
    }
    int revokeSecond = 120;
    var currentSeconds = (DateTime.now().millisecondsSinceEpoch / 1000).toInt();
    return (currentSeconds - timestamp < revokeSecond &&
            fromUID == CacheHelper.uid &&
            status == WKSendMsgResult.sendSuccess) ||
        (isManager && status == WKSendMsgResult.sendSuccess);
  }
}
