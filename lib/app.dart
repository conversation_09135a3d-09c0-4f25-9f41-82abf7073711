import 'dart:async';

import 'package:and/bloc/navigation/navi_count_bloc.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/common/theme/app_theme.dart';
import 'package:and/l10n/generated/app_localizations.dart';
import 'package:and/l10n/language_utils.dart';
import 'package:and/module/launch/splash_screen.dart';
import 'package:and/module/main_page.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/install_referrer_utils.dart';
import 'package:and/utils/deeplink_utils.dart';
import 'package:app_links/app_links.dart';
import 'package:event_bus/event_bus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:get/get_navigation/src/routes/transitions_type.dart'
    as GetTransition;
import 'package:multi_trigger_autocomplete/multi_trigger_autocomplete.dart';
import 'package:oktoast/oktoast.dart' as OkToast;
import 'package:toastification/toastification.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey();
final RouteObserver<PageRoute> routeObserver = RouteObserver();
final GlobalRouteObserver globalRouterObserver = GlobalRouteObserver();

BuildContext? get globalContext => navigatorKey.currentContext;

class GlobalRouteObserver extends GetObserver {
  final List<String> _routeStack = [];

  @override
  void didPush(Route route, Route? previousRoute) {
    super.didPush(route, previousRoute);
    _routeStack.add(route.settings.name ?? 'unknown'); // 添加到栈中
  }

  @override
  void didPop(Route route, Route? previousRoute) {
    super.didPop(route, previousRoute);
    _routeStack.removeLast(); // 从栈中移除
  }

  @override
  void didRemove(Route route, Route? previousRoute) {
    super.didRemove(route, previousRoute);
    _routeStack.remove(route.settings.name ?? 'unknown'); // 从栈中移除
  }

  List<String> getRouteStack() {
    return List.from(_routeStack); // 获取当前路由栈
  }

  bool containRoute(String name) {
    return getRouteStack().contains(name);
  }
}

final eventBus = EventBus();

class App extends StatefulWidget {
  bool _init = true;

  App({super.key});

  @override
  State createState() => AppState();
}

class AppState extends State<App> {
  static AppSetting setting = AppSetting.instance;

  late AppLinks _appLinks;
  StreamSubscription<Uri>? _linkSubscription;

  @override
  void initState() {
    super.initState();
    initApp();
    initDeepLinks();
  }

  @override
  void dispose() {
    _linkSubscription?.cancel();

    super.dispose();
  }

  Future<void> initDeepLinks() async {
    _appLinks = AppLinks();

    // Handle links
    _linkSubscription = _appLinks.uriLinkStream.listen((uri) {
      debugPrint('onAppLink: $uri');

      Get.engine.addPostFrameCallback((timeStamp) {
        openAppLink(uri);
      });
    });
  }

  void openAppLink(Uri uri) {
    DeeplinkUtils.handleDeepLinkUri(uri);
  }

  Future<void> initApp() async {
    //第一次进入app时，获取本地多语言的countryCode
    if (widget._init) {
      setting.setLocale();
      widget._init = false;
    }
    // 更改语言
    setting.changeLocale = () {
      if (mounted) {
        setState(() {});
      }
    };
  }

  @override
  Widget build(BuildContext context) {
    return Portal(
        child: MultiBlocProvider(
            providers: [
          BlocProvider<NaviCountBloc>(
            create: (context) => NaviCountBloc(),
          ),
        ],
            child: OkToast.OKToast(
                textPadding:
                    const EdgeInsets.only(left: 8, right: 8, top: 5, bottom: 5),
                radius: 20,
                child: ToastificationWrapper(child: _buildApp(context)))));
  }

  Widget _buildApp(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      defaultTransition: GetTransition.Transition.rightToLeft,
      builder: EasyLoading.init(builder: (BuildContext context, Widget? child) {
        double scale = CacheHelper.caringModel ? 1.4 : 1.0;
        return MediaQuery(
          data: MediaQuery.of(context)
              .copyWith(textScaler: TextScaler.linear(scale)),
          child: child ?? Container(),
        );
      }),
      title: 'Greezy',
      theme: AppTheme.lightTheme,
      localizationsDelegates: AppLocalizations.localizationsDelegates,
      supportedLocales: AppLocalizations.supportedLocales,
      locale: setting._locale,
      fallbackLocale: const Locale("en", "US"),
      localeResolutionCallback: (deviceLocale, supportedLocales) {
        if (kDebugMode) {
          print('当前语言：${deviceLocale.toString()}');
        }
        setting.setLocale();
        return setting._locale;
      },
      home: const MainPage(),
      getPages: RouteGet.getPages,
      navigatorKey: navigatorKey,
      navigatorObservers: [routeObserver, globalRouterObserver],
    );
  }
}

class AppSetting {
  AppSetting._();

  static final AppSetting _instance = AppSetting._();

  static AppSetting get instance => _instance;
  Locale? _locale;

  Function()? changeLocale;

  void setLocale() {
    _locale = LanguageUtils.getLocale();
  }
}
