import 'dart:async';

import 'package:and/module/chat/controller/chat_msg_controller.dart';
import 'package:flutter/material.dart';
import 'package:super_sliver_list/super_sliver_list.dart';

import 'chat_operation.dart';

class ChatScrollController {
  final ScrollController scrollController = ScrollController();
  final ListController listController = ListController();

  final _operationsController = StreamController<PullMode>.broadcast();

  ChatScrollController() {
    scrollController.addListener(_onScrollLister);
  }

  _onScrollLister() {
    // 判断内容是否超过一屏
    if (scrollController.position.maxScrollExtent <= 0) {
      return;
    }

    if (scrollController.position.pixels >=
        scrollController.position.maxScrollExtent) {
      _operationsController.add(PullMode.pullDown);
    } else if (scrollController.position.pixels == 0) {
      _operationsController.add(PullMode.pullUp);
    }
  }

  Stream<PullMode> get operationsStream => _operationsController.stream;

  void dispose() {
    scrollController.removeListener(_onScrollLister);
    scrollController.dispose();
    listController.dispose();
  }

  void onOperationChanged(ChatOperation operation) {
    switch (operation.type) {
      case ChatOperationType.insert:
      case ChatOperationType.update:
      case ChatOperationType.remove:
      case ChatOperationType.set:
    }
  }

  jumpToPosition(int position, {int delay = 50, double alignment = 0.5}) {
    print("jumpToPosition: $position alignment: $alignment");
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(Duration(milliseconds: delay), () {
        try {
          listController.jumpToItem(
            index: position,
            scrollController: scrollController,
            alignment: alignment,
          );
        } catch (e) {
          print("Error in jumpToItem: $e");
        }
      });
    });
  }

  jumpToTop({int delay = 0}) {
    if (listController.numberOfItems == 0) {
      return;
    }
    jumpToPosition(listController.numberOfItems - 1, delay: delay);
  }

  jumpToBottom({int delay = 300}) {
    if (listController.numberOfItems == 0) {
      return;
    }
    jumpToPosition(0, delay: delay, alignment: 0);
  }

  animateToPosition(int position, {int delay = 50, double alignment = 1}) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      listController.animateToItem(
          index: position,
          scrollController: scrollController,
          alignment: alignment,
          duration: (distance) {
            return Duration(milliseconds: 300);
          },
          curve: (distance) {
            return Curves.easeInOut;
          });
    });
  }

  animateToBottom({int delay = 300}) {
    if (listController.numberOfItems == 0) {
      return;
    }
    animateToPosition(0, delay: delay, alignment: 0);
  }

  animateToTop({int delay = 300}) {
    if (listController.numberOfItems == 0) {
      return;
    }
    animateToPosition(listController.numberOfItems - 1, delay: delay);
  }

}
