import 'dart:async';
import 'dart:io';

import 'package:and/app.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/constant/wk_cmd_keys.dart';
import 'package:and/constant/wk_message_content_type_ext.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/call_response.dart';
import 'package:and/model/content/wk_invite_call_content.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/model/request/call_request.dart';
import 'package:and/module/videocall/videocall_argument.dart';
import 'package:and/module/videocall/videocall_page.dart';
import 'package:and/utils/audio_play_manager.dart';
import 'package:and/utils/audio_utils.dart';
import 'package:and/utils/file_path.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/utils/image_utils.dart';
import 'package:and/utils/livekit_config.dart';
import 'package:and/utils/notification_utils.dart';
import 'package:and/utils/permission_check_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:livekit_client/livekit_client.dart';
import 'package:oktoast/oktoast.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/cmd.dart';
import 'package:wukongimfluttersdk/entity/conversation.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

enum VideoCallUIState {
  calling, // 发起通话
  receiving, // 收到通话邀请
  connected // 通话中
}

enum DisplayMode {
  banner, // 横幅邀请模式
  fullscreen, // 全屏模式
  floating // 悬浮小窗模式
}

class VideoCallLogic extends GetxController {
  static OverlayEntry? _entry;

  // 是否在通话中
  static final RxBool isInCall = false.obs;

  static final Rx<VideoCallUIState> uiState = VideoCallUIState.calling.obs;

  final Rx<DisplayMode> displayMode = DisplayMode.fullscreen.obs;

  final RxBool isLocalVideoEnabled = false.obs;

  final RxBool isLocalAudioEnabled = true.obs;

  // 是否开启扬声器
  final RxBool isSpeakerEnabled = false.obs;

  final RxBool isLocalScreenShareEnabled = false.obs;

  // 远程参与者列表
  final RxList<Participant> participants = <Participant>[].obs;

  // 悬浮窗位置
  final Rx<Offset> floatingPosition = Offset(20, 50).obs;

  // 悬浮窗大小
  final floatingSize = const Size(120, 160);

  // banner模式高度
  double bannerHeight = 100;

  /// 音视频通话本地推送固定个推送消息ID方便取消/超时时覆盖邀请通话推送
  /// 由于邀请cmd跟取消/超时消息中没有相同的int型ID顾用时间戳为每通话固定一个ID
  static int callPushId = 10000;

  /// 临时存储邀请通话相关参数 通话取消/超时时更新本地推送用
  static VideoCallArgument? inviteCallArgument;

  final RxList<LocalParticipant> localParticipants = <LocalParticipant>[].obs;

  late final EventsListener<RoomEvent> _listener = _room.createListener();

  /// 同步断开连接期间收到的历史消息，用于判断二次收到的channelid以___cmd结尾的通话邀请是否处理
  /// 支持WKUIConversationMsg和WKMsg两种类型
  static List<dynamic> pendingMsgs = [];
  static String? currentRoomId;

  void _onRoomDidUpdate() {
    participants.value = _room.remoteParticipants.values.toList();
    // 当有参与者加入时，更新UI状态为connected
    if (participants.isNotEmpty) {
      _openAudioPermission();
      uiState.value = VideoCallUIState.connected;
      _startCallTimer();
      AudioPlayManager().stop();
    } else {
      // 当所有远程参与者都断开连接时，说明对方可能异常退出
      if (uiState.value == VideoCallUIState.connected) {
        callToast(Get.context!.l10n.callEnded);
      }
      leaveRoom();
    }
  }

  Rx<CameraPosition> position = CameraPosition.front.obs;

  /// 监听参与者的音视频状态变化
  void _onParticipantDidUpdate(ParticipantEvent event) {
    _onRoomDidUpdate();
  }

  // 通话时长（秒）
  final RxInt callDuration = 0.obs;
  Timer? _callTimer;

  late Room _room;

  Future<void> initializeCall() async {
    try {
      _room = Room(
        roomOptions: RoomOptions(
          adaptiveStream: true,
          dynacast: true,
        ),
      );

      _listener
        ..on<RoomDisconnectedEvent>((_) {
          _onRoomDidUpdate();
        })
        ..on<ParticipantDisconnectedEvent>((e) {
          _onRoomDidUpdate();
        })
        ..on<ParticipantConnectedEvent>((e) {
          _onRoomDidUpdate();
        })
        ..on<TrackSubscribedEvent>((e) {
          _onParticipantDidUpdate(e);
        })
        ..on<TrackUnsubscribedEvent>((e) {
          _onParticipantDidUpdate(e);
        })
        ..on<TrackMutedEvent>((e) {
          if (e.participant is RemoteParticipant) {
            _onParticipantDidUpdate(e);
          }
        })
        ..on<TrackUnmutedEvent>((e) {
          if (e.participant is RemoteParticipant) {
            _onParticipantDidUpdate(e);
          }
        });
    } catch (e) {
      callToast(Get.context!.l10n.callFailed);
    }
  }

  /// 通过rx更新当前用户信息
  void _updateLocalParticipant() {
    localParticipants.value = [_room.localParticipant!];
    localParticipants.refresh();
  }

  /// 加入房间
  Future<bool> joinRoom(String roomName, String token) async {
    try {
      _room.setSpeakerOn(isSpeakerEnabled.value, forceSpeakerOutput: true);

      await _room.connect(
        LiveKitConfig.host,
        token,
      );

      // 先同步下本地用户信息，防止开启摄像头之前显示错误页面
      _updateLocalParticipant();
      if (isLocalAudioEnabled.value && Platform.isIOS) {
        try {
          await _room.localParticipant?.setMicrophoneEnabled(true);
        } catch (e) {
          print('Failed to enable camera: $e');
          isLocalAudioEnabled.value = false;
        }
      }

      if (isLocalVideoEnabled.value) {
        try {
          await _room.localParticipant?.setCameraEnabled(true);
        } catch (e) {
          print('Failed to enable camera: $e');
          isLocalVideoEnabled.value = false;
        }
      }
      _updateLocalParticipant();

      _startForegroundService();

      return true;
    } catch (e) {
      return false;
    }
  }

  /// 切换本地视频状态
  Future<void> toggleLocalVideo() async {
    try {
      final bool newState = !isLocalVideoEnabled.value;
      await _room.localParticipant?.setCameraEnabled(newState);

      isLocalVideoEnabled.value = newState;

      _updateLocalParticipant();
    } catch (e) {
      print('Error toggling video: $e');
      if (!await ImageUtils.requestCameraPermission()) {
        ImageUtils.showRequestCameraPermission();
      } else {
        callToast('Failed to toggle video');
      }
    }
  }

  /// 安卓设备正常播放铃声中设置麦克风权限(setMicrophoneEnabled(true))后铃声会没掉
  /// 所以等接通后再开启麦克风权限
  Future<void> _openAudioPermission() async {
    if (Platform.isAndroid) {
      // 请求音频和唤醒锁权限
      await [Permission.microphone, Permission.ignoreBatteryOptimizations]
          .request();
    }
    if (isLocalAudioEnabled.value) {
      try {
        await _room.localParticipant?.setMicrophoneEnabled(true);
      } catch (e) {
        print('Failed to enable camera: $e');
        isLocalAudioEnabled.value = false;
      }
    }
  }

  /// 切换本地音频状态
  Future<void> toggleLocalAudio() async {
    try {
      final bool newState = !isLocalAudioEnabled.value;
      await _room.localParticipant?.setMicrophoneEnabled(newState);
      isLocalAudioEnabled.value = newState;
      _updateLocalParticipant();
    } catch (e) {
      print('Error toggling audio: $e');
      if (!await AudioUtils.requestMicrophonePermission()) {
        AudioUtils.showRequestMicrophonePermission();
      } else {
        callToast('Failed to toggle audio');
      }
    }
  }

  /// 切换扬声器状态
  Future<void> toggleSpeaker() async {
    try {
      final bool newState = !isSpeakerEnabled.value;
      await _room.setSpeakerOn(newState, forceSpeakerOutput: true);
      isSpeakerEnabled.value = newState;
    } catch (e) {
      print('Error toggling speaker: $e');
      callToast('Failed to toggle speaker');
    }
  }

  /// 离开房间
  Future<void> leaveRoom() async {
    _room.disconnect();
    _listener.dispose();
    _stopCallTimer();
    hide();
  }

  /// 切换摄像头(前置/后置)
  Future<void> switchCamera() async {
    final track =
        localParticipants.first.videoTrackPublications.firstOrNull?.track;
    if (track == null) return;

    try {
      final newPosition = position.value.switched();
      await track.setCameraPosition(newPosition);
      position.value = newPosition;
    } catch (error) {
      print('could not restart track: $error');
      return;
    }
  }

  static void handleCall(WKMsg msg) {
    if (currentRoomId == null || !msg.content.contains(currentRoomId!)) {
      return;
    }
    switch (msg.contentType) {
      case WkMessageContentTypeExt.inviteGroupCall:
        // showCall(msg);
        break;
      case WkMessageContentTypeExt.cancelCall:
        if (uiState.value != VideoCallUIState.calling) {
          callToast(Get.context!.l10n.callCanceledByPeer);
        }
        hide();
        _showVideoCalLocalPush();
        break;
      case WkMessageContentTypeExt.callTimeOut:
        if (uiState.value != VideoCallUIState.connected) {
          hide();
          _showVideoCalLocalPush();
        }
        break;
      case WkMessageContentTypeExt.rejectCall:
        if (uiState.value != VideoCallUIState.connected) {
          if (uiState.value == VideoCallUIState.calling) {
            callToast(Get.context!.l10n.callRejected);
          } else {
            callToast(Get.context!.l10n.callReject);
          }
          hide();
        }
        break;
      case WkMessageContentTypeExt.endCall:
        if (uiState.value == VideoCallUIState.connected) {
          callToast(Get.context!.l10n.callEnded);
          hide();
        }
        break;
    }
  }

  /// 是否为通话消息(邀请通话cmd消息除外)
  static bool isCallMsg(WKMsg msg) {
    return WkMessageContentTypeExt.isCallMsg(msg.contentType);
  }

  /// 显示通话相关的提示信息
  static void callToast(String message) {
    if (isInCall.value) {
      showToast(message);
    }
  }

  /// 邀请通话
  static Future<void> inviteCall(VideoCallArgument argument) async {
    if (isInCall.value) {
      showToast(Get.context!.l10n.inCall);
      return;
    }

    CallResponse? response;
    EasyLoading.show();
    if (argument.channel.channelType == WKChannelType.group) {
      response = await HttpUtils.startGroupCall(argument.channel.channelID);
    } else {
      response = await HttpUtils.startSingleCall(
          argument.channel.channelID, argument.callType.value);
    }
    EasyLoading.dismiss();
    if (response != null && response.roomId != null) {
      argument.roomId = response.roomId;
      argument.livekitToken = response.livekitToken;
      argument.callUIState = VideoCallUIState.calling;
      show(argument, isInvite: true);
    } else {
      callToast(Get.context!.l10n.networkError);
    }
  }

  /// 显示被邀请通话页面
  static Future<void> showCall(WKCMD cmd) async {
    if (cmd.param["from_uid"] == CacheHelper.uid) {
      return;
    }

    WkInviteCallContent content = WkInviteCallContent();
    Map<String, dynamic> jsonData = {
      'cmd': cmd.cmd,
      'type': 99,
      'param': cmd.param
    };
    content.decodeJson(jsonData);

    if (await _didCallFinished(content.roomId)) {
      return;
    }
    if (content.channelId.endsWith(WKCMDKeys.wkCMDEnd)) {
      content.channelId = content.channelId.replaceAll(WKCMDKeys.wkCMDEnd, "");
    }

    WKChannel channel = await WKIM.shared.channelManager
            .getChannel(content.channelId, content.channelType) ??
        WKChannel(content.channelId, content.channelType);

    VideoCallArgument argument = VideoCallArgument(
        callType: content.callType == 1 ? CallType.audio : CallType.video,
        roomId: content.roomId,
        livekitToken: content.livekitToken,
        channel: channel);
    argument.callUIState = VideoCallUIState.receiving;
    show(argument);

    inviteCallArgument = argument;

    /// 推送ID只能是32为int 所以不能直接取当前时间秒数
    final date = DateTime.now();
    String joined = '${date.day}${date.hour}${date.minute}${date.second}';
    callPushId = int.parse(joined);

    _showVideoCalLocalPush(isInvite: true);
  }

  /// 加入通话
  /// isInviter 是否为通话邀请者（通话发起人）
  Future<void> joinCall(VideoCallArgument argument,
      {bool isInviter = false}) async {
    if (argument.roomId == null || (argument.roomId ?? "").isEmpty) {
      return;
    }

    CallRequest request = CallRequest(
        roomId: argument.roomId!, callType: argument.callType.value);
    // 邀请人调加入通话接口主要是防止被邀请人加入通话接口调用失败直接进房间导致通话时长计算有问题
    if (isInviter) {
      HttpUtils.joinCall(
          argument.channel.channelID, argument.channel.channelType, request);
      return;
    }

    EasyLoading.show();
    await initializeCall();
    final joined = await joinRoom(argument.roomId!, argument.livekitToken!);
    if (!joined) {
      callToast(Get.context!.l10n.joinCallFailed);
      leaveRoom();
      return;
    }
    _onRoomDidUpdate();
    displayMode.value = DisplayMode.fullscreen;
    HttpUtils.joinCall(
        argument.channel.channelID, argument.channel.channelType, request);

    EasyLoading.dismiss();
  }

  Future<void> rejectCall(VideoCallArgument argument) async {
    if (argument.roomId == null || (argument.roomId ?? "").isEmpty) {
      return;
    }
    EasyLoading.show();
    CallRequest request = CallRequest(
        roomId: argument.roomId!, callType: argument.callType.value);
    await HttpUtils.rejectCall(
        argument.channel.channelID, argument.channel.channelType, request);
    EasyLoading.dismiss();
    hide();
  }

  // 切换显示模式
  void toggleDisplayMode() {
    if (displayMode.value == DisplayMode.fullscreen) {
      displayMode.value = DisplayMode.floating;
    } else {
      displayMode.value = DisplayMode.fullscreen;
    }
  }

  // 更新悬浮窗位置
  void updateFloatingPosition(Offset newPosition) {
    floatingPosition.value = newPosition;
  }

  Future<void> hangupCall(VideoCallArgument argument) async {
    /// 结束通话先退房间再调接口 否则取消通话会被认为是结束通话
    leaveRoom();

    if (argument.roomId == null || (argument.roomId ?? "").isEmpty) {
      return;
    }
    CallRequest request = CallRequest(
        roomId: argument.roomId!, callType: argument.callType.value);
    HttpUtils.hangupCall(
        argument.channel.channelID, argument.channel.channelType, request);
  }

  Future<void> cancelCall(VideoCallArgument argument) async {
    leaveRoom();

    if (argument.roomId == null || (argument.roomId ?? "").isEmpty) {
      return;
    }
    CallRequest request = CallRequest(
        roomId: argument.roomId!, callType: argument.callType.value);
    HttpUtils.cancelCall(
        argument.channel.channelID, argument.channel.channelType, request);
  }

  /// 共享屏幕
  void shareScreen() {
    _room.localParticipant?.setScreenShareEnabled(true);
  }

  /// 显示通话页面
  static void show(VideoCallArgument arguments, {bool isInvite = false}) {
    if (_entry != null) return;
    _entry = OverlayEntry(
        builder: (context) => VideoCallPage(argument: arguments),
        canSizeOverlay: false);
    final BuildContext? context = Get.context ?? globalContext;
    if (context != null) {
      Overlay.of(context, rootOverlay: true).insert(_entry!);
      isInCall.value = true;
      currentRoomId = arguments.roomId;
      if (isInvite) {
        // 播放呼出铃声
        AudioPlayManager()
            .play(FilePath.call_outgoing, followMute: true, isLoop: true);
      } else {
        // 播放来电铃声
        AudioPlayManager()
            .play(FilePath.call_incoming, followMute: true, isLoop: true);
      }
    }
  }

  /// 关闭通话页面
  static void hide() {
    _entry?.remove();
    _entry = null;
    isInCall.value = false;
    // 停止铃声播放
    AudioPlayManager().stop();
    EasyLoading.dismiss();
    _stopForegroundService();
  }

  // 开始计时
  void _startCallTimer() {
    if (_callTimer != null) {
      return;
    }
    _callTimer?.cancel();
    callDuration.value = 0;
    _callTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      callDuration.value++;
    });
  }

  // 停止计时
  void _stopCallTimer() {
    _callTimer?.cancel();
    _callTimer = null;
    callDuration.value = 0;
  }

  /// android应用退后台/锁屏后麦克风会停止工作 需要启动前台服务
  void _startForegroundService() async {
    if (Platform.isIOS) {
      return;
    }
    if (!(await PermissionCheckUtils
        .checkForegroundMicrophoneServicePermission())) {
      print("没有麦克风权限");
      return;
    }
    if (globalContext != null) {
      FlutterLocalNotificationsPlugin()
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.startForegroundService(callPushId, globalContext!.l10n.inCall, "",
              notificationDetails: AndroidNotificationDetails(
                'channel_id',
                'channel_name',
                importance: Importance.max,
                priority: Priority.max,
              ),
              foregroundServiceTypes: {
            AndroidServiceForegroundType.foregroundServiceTypeMicrophone
          });
    }
  }

  static void _stopForegroundService() async {
    if (Platform.isIOS) {
      return;
    }
    if (!(await PermissionCheckUtils
        .checkForegroundMicrophoneServicePermission())) {
      print("没有麦克风权限");
      return;
    }
    FlutterLocalNotificationsPlugin()
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.stopForegroundService();
  }

  // 格式化通话时长
  String getFormattedCallDuration() {
    final duration = Duration(seconds: callDuration.value);
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String hours =
        duration.inHours > 0 ? '${twoDigits(duration.inHours)}:' : '';
    String minutes = twoDigits(duration.inMinutes.remainder(60));
    String seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$hours$minutes:$seconds';
  }

  static void _showVideoCalLocalPush({bool isInvite = false}) {
    if (inviteCallArgument == null || inviteCallArgument?.channel.mute == 1) {
      return;
    }

    String title = inviteCallArgument?.channel.displayName ?? '';

    String? content;
    if (isInvite) {
      content = inviteCallArgument?.callType == CallType.audio
          ? (globalContext?.l10n.audioCall ?? 'Audio Call')
          : (globalContext?.l10n.videoCall ?? 'Video Call');
    } else {
      content = globalContext?.l10n.missedCall ?? 'Missed Call';
    }
    content = '[$content]';

    NotificationUtils.showVideoCallLocalNotification(
        pushId: callPushId, title: title, content: content, isInvite: isInvite);
  }

  /// 通话是否已完成（已结束/取消/超时）
  static Future<bool> _didCallFinished(String roomId) async {
    for (var msg in pendingMsgs) {
      WKMsg? wkMsg;
      if (msg is WKUIConversationMsg) {
        wkMsg = await msg.getWkMsg();
      } else if (msg is WKMsg) {
        wkMsg = msg;
      }
      if (wkMsg != null && isCallMsg(wkMsg) && wkMsg.content.contains(roomId)) {
        return true;
      }
    }
    return false;
  }
}
