name: and
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.4.0+2025052719

environment:
  sdk: ^3.6.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  get: ^4.6.5
  get_storage: ^2.1.1
  dio: ^5.4.3+1
  cached_network_image: ^3.2.3
  oktoast: ^3.1.0
  flutter_easyloading: ^3.0.5
  json_annotation: ^4.9.0
  retrofit: ^4.4.2
  bloc: ^9.0.0
  flutter_bloc: ^9.0.0
  permission_handler: ^12.0.0+1
  event_bus: ^2.0.1
  table_calendar: ^3.1.2
  mask_text_input_formatter: ^2.9.0
  app_settings: ^6.1.1
  provider: ^6.1.2
  webview_flutter: ^4.9.0
  url_launcher: ^6.2.6
  pinput: ^5.0.0
  flutter_svg: ^2.0.10+1
  date_format: ^2.0.7
  image_picker: ^1.1.2
  image_cropper: ^9.0.0
  flutter_image_compress: ^2.4.0
  smooth_page_indicator: ^1.2.0+3
  flutter_widget_from_html: ^0.15.3
  #  flutter_html: ^3.0.0-beta.2
  connectivity_plus: ^6.0.5
  google_sign_in: ^6.3.0
  sign_in_with_apple: ^7.0.1
  email_validator: ^3.0.0
  shared_preferences: ^2.3.2
  flutter_animate: ^4.5.0
  material_dialogs: ^1.1.5
  copy_with_extension: ^6.0.1
  flutter_custom_tabs: ^2.1.0
  loading_animation_widget: ^1.2.1
  shimmer: ^3.0.0
  timelines_plus: ^1.0.3
  intl: any
  carousel_slider: ^5.0.0
  easy_stepper: ^0.8.5+1
  firebase_analytics: ^11.4.6
  firebase_core: ^3.12.1
  firebase_messaging: ^15.2.4
  flutter_local_notifications: ^19.1.0
  uuid: ^4.3.0
  flutter_zoom_drawer: ^3.2.0
  dotted_line: ^3.2.2
  omni_datetime_picker: ^2.0.5
  flutter_multi_formatter: ^2.13.7
  dotted_border: ^2.1.0
  package_info_plus: ^8.0.3
  flutter_expanded_tile: ^0.4.0
  extended_image: ^9.0.4
  flutter_inappwebview: ^6.1.5
  sliver_tools: ^0.2.12
  scroll_to_index: ^3.0.1
  in_app_update: ^4.2.3
  flutter_secure_storage: ^9.2.2
  regexpattern: ^2.6.0
  flutter_swipe_action_cell: ^3.1.5
  flutter_native_splash: ^2.4.2
  flutter_mute: ^0.0.4
  scrollview_observer: ^1.23.0
  flutter_form_builder: ^10.0.1
  app_links: ^6.4.0
  form_builder_validators: ^11.0.0
  toastification: ^3.0.0
  flutter_staggered_grid_view: ^0.7.0
  ulid: ^2.0.1
  wukongimfluttersdk: ^1.6.0
  device_info_plus: ^11.2.2
  flutter_device_id: ^1.0.1
  tmp_path: ^1.3.3
  video_player: ^2.9.5
  flutter_keyboard_visibility: ^6.0.0
  super_sliver_list: ^0.4.1
  mobile_scanner: ^7.0.0
  pretty_qr_code: ^3.3.0
  lottie: ^3.3.1
  easy_refresh: ^3.4.0
  pinyin: ^3.3.0
  chat_bottom_container: ^0.3.1
  emoji_picker_flutter: ^4.3.0
  image_gallery_saver_plus: ^4.0.1
  record: ^6.0.0
  just_audio: ^0.9.46
  flutter_spinkit: ^5.2.1
  background_downloader: ^9.2.2
  visibility_detector: ^0.4.0+2
  vibration: ^3.1.3
  multi_trigger_autocomplete: ^1.0.0
  extended_text: ^15.0.0
  extended_text_field: ^16.0.2
  flutter_app_badger: ^1.5.0
  custom_text: ^2.0.3
  country_codes: ^3.3.0
  wechat_assets_picker: ^9.5.0
  mime: ^2.0.0
  better_player_plus: ^1.0.8
  livekit_client: ^2.4.1
  file_picker: ^10.0.0
  open_file: ^3.5.10
  downloadsfolder: ^1.1.0
  ota_update: ^7.0.1
  get_thumbnail_video: ^0.7.3
  android_play_install_referrer: ^0.4.0
  restart_app: ^1.3.2
  native_string_res: ^0.0.5
  firebase_crashlytics: ^4.3.6
  advertising_id: ^2.7.1
  slider_captcha: ^1.0.2

dependency_overrides:
  wukongimfluttersdk:
    path: third/wukongimfluttersdk

dev_dependencies:
  flutter_test:
    sdk: flutter

  retrofit_generator: ^9.1.2
  build_runner: ^2.3.3
  json_serializable: ^6.8.0
  copy_with_extension_gen: ^6.0.1
  flutter_launcher_icons: ^0.14.3

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/files/
    - assets/images/
    - assets/lottie/
    - assets/svgs/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/app_icon.png"
  adaptive_icon_background: "#ffffff" # Android 适配图标背景色（可选）
  #remove_alpha_ios: true  # ⚠️ 关键配置：去除 iOS 图标的透明通道
  #adaptive_icon_foreground: "" # 适配前景图（可选）

flutter_native_splash:
  color: "#ffffff"  # 启动屏幕背景色
  image: assets/images/ic_logo.png  # 启动屏幕图标

  android_12:
    color: "#ffffff"
    icon_background_color: "#ffffff"  # Android 12 背景色
